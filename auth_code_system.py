#!/usr/bin/env python3
"""
Sistema di autenticazione con codice di 6 cifre per SNIP
Genera un codice ad ogni riavvio dell'app, lo invia via email e richiede l'inserimento per l'accesso
"""

import random
import string
import json
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
import logging

logger = logging.getLogger(__name__)

class AuthCodeSystem:
    """Sistema di autenticazione con codice di 6 cifre"""

    def __init__(self):
        self.code_file = "auth_code.json"
        self.current_code = None
        self.code_generated_at = None
        self.code_expires_at = None
        self.is_authenticated = False
        self.target_email = "<EMAIL>"

    def generate_code(self) -> str:
        """Genera un nuovo codice di 6 cifre"""
        # Genera codice numerico di 6 cifre
        code = ''.join(random.choices(string.digits, k=6))

        # Salva il codice con timestamp
        now = datetime.now()
        expires = now + timedelta(hours=24)  # Il codice scade dopo 24 ore

        self.current_code = code
        self.code_generated_at = now
        self.code_expires_at = expires
        self.is_authenticated = False

        # Salva su file per persistenza
        self._save_code_to_file()

        logger.info(f"🔐 Nuovo codice di autenticazione generato: {code}")
        return code

    def _save_code_to_file(self):
        """Salva il codice corrente su file"""
        try:
            data = {
                "code": self.current_code,
                "generated_at": self.code_generated_at.isoformat() if self.code_generated_at else None,
                "expires_at": self.code_expires_at.isoformat() if self.code_expires_at else None,
                "is_authenticated": self.is_authenticated,
                "target_email": self.target_email
            }

            with open(self.code_file, 'w') as f:
                json.dump(data, f, indent=2)

            logger.debug(f"Codice salvato su file: {self.code_file}")

        except Exception as e:
            logger.error(f"Errore salvataggio codice su file: {e}")

    def _load_code_from_file(self) -> bool:
        """Carica il codice dal file se esiste"""
        try:
            if not os.path.exists(self.code_file):
                return False

            with open(self.code_file, 'r') as f:
                data = json.load(f)

            self.current_code = data.get("code")
            self.is_authenticated = data.get("is_authenticated", False)
            self.target_email = data.get("target_email", "<EMAIL>")

            # Carica timestamp
            if data.get("generated_at"):
                self.code_generated_at = datetime.fromisoformat(data["generated_at"])
            if data.get("expires_at"):
                self.code_expires_at = datetime.fromisoformat(data["expires_at"])

            logger.debug(f"Codice caricato da file: {self.code_file}")
            return True

        except Exception as e:
            logger.error(f"Errore caricamento codice da file: {e}")
            return False

    def is_code_valid(self) -> bool:
        """Verifica se il codice corrente è ancora valido"""
        if not self.current_code or not self.code_expires_at:
            return False

        return datetime.now() < self.code_expires_at

    def verify_code(self, input_code: str) -> bool:
        """Verifica se il codice inserito è corretto"""
        if not self.current_code or not self.is_code_valid():
            logger.warning("Tentativo di verifica con codice scaduto o inesistente")
            return False

        # Rimuovi spazi e confronta
        input_code = input_code.strip().replace(" ", "")

        if input_code == self.current_code:
            self.is_authenticated = True
            self._save_code_to_file()
            logger.info(f"✅ Codice verificato con successo: {input_code}")
            return True
        else:
            logger.warning(f"❌ Codice errato inserito: {input_code} (atteso: {self.current_code})")
            return False

    def send_code_email(self, db: Session) -> bool:
        """Invia il codice via email usando il sistema email esistente"""
        try:
            if not self.current_code:
                logger.error("Nessun codice da inviare")
                return False

            # Importa la funzione send_email dal main
            from main import send_email

            subject = "🔐 Codice di Accesso SNIP - Sistema Navale Integrato Portuale"

            body = f"""
Gentile Amministratore,

Il sistema SNIP è stato riavviato e richiede l'autenticazione per motivi di sicurezza.

🔑 CODICE DI ACCESSO: {self.current_code}

Questo codice è necessario per accedere al sistema e deve essere inserito nella pagina di login.

📋 DETTAGLI:
• Codice generato: {self.code_generated_at.strftime('%d/%m/%Y alle %H:%M:%S') if self.code_generated_at else 'N/A'}
• Codice scade: {self.code_expires_at.strftime('%d/%m/%Y alle %H:%M:%S') if self.code_expires_at else 'N/A'}
• Validità: 24 ore dalla generazione

⚠️ IMPORTANTE:
- Questo codice è valido solo per questa sessione
- Non condividere questo codice con nessuno
- Il sistema si chiuderà automaticamente se il codice non viene inserito correttamente

🔒 SICUREZZA:
Il sistema richiede questo codice ad ogni riavvio per garantire che solo gli utenti autorizzati possano accedere al sistema SNIP.

---
Sistema di Sicurezza SNIP
Michele Autuori Srl - shipping and forwarding agency
Generato automaticamente il {datetime.now().strftime('%d/%m/%Y alle %H:%M:%S')}
            """

            # Invia email
            success = send_email(
                to_email=self.target_email,
                subject=subject,
                body=body,
                db=db
            )

            if success:
                logger.info(f"📧 Codice inviato via email a: {self.target_email}")
                return True
            else:
                logger.error(f"❌ Errore invio email a: {self.target_email}")
                return False

        except Exception as e:
            logger.error(f"Errore invio codice via email: {e}")
            return False

    def initialize_system(self, db: Session) -> bool:
        """Inizializza il sistema di autenticazione"""
        try:
            # Carica codice esistente se presente
            code_loaded = self._load_code_from_file()

            # Se non c'è codice o è scaduto, genera nuovo
            if not code_loaded or not self.is_code_valid() or not self.is_authenticated:
                logger.info("🔄 Generazione nuovo codice di autenticazione...")
                self.generate_code()

                # Invia il codice via email
                email_sent = self.send_code_email(db)
                if not email_sent:
                    logger.error("❌ Impossibile inviare il codice via email")
                    return False

                logger.info("✅ Sistema di autenticazione inizializzato con successo")
                return True
            else:
                logger.info("✅ Sistema già autenticato con codice valido")
                return True

        except Exception as e:
            logger.error(f"Errore inizializzazione sistema autenticazione: {e}")
            return False

    def reset_authentication(self):
        """Reset dell'autenticazione (per test o riavvio forzato)"""
        self.is_authenticated = False
        self._save_code_to_file()
        logger.info("🔄 Autenticazione resettata")

    def cleanup_old_codes(self):
        """Pulisce i codici scaduti"""
        if self.code_expires_at and datetime.now() > self.code_expires_at:
            logger.info("🧹 Pulizia codice scaduto")
            self.current_code = None
            self.code_generated_at = None
            self.code_expires_at = None
            self.is_authenticated = False
            self._save_code_to_file()

    def get_status(self) -> Dict[str, Any]:
        """Ottieni lo stato corrente del sistema"""
        return {
            "has_code": bool(self.current_code),
            "is_valid": self.is_code_valid(),
            "is_authenticated": self.is_authenticated,
            "generated_at": self.code_generated_at.isoformat() if self.code_generated_at else None,
            "expires_at": self.code_expires_at.isoformat() if self.code_expires_at else None,
            "target_email": self.target_email
        }

# Istanza globale del sistema
auth_code_system = AuthCodeSystem()
