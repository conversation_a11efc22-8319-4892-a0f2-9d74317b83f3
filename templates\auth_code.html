<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Codice di Sicurezza - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --security-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --shadow-soft: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-hover: 0 15px 35px rgba(31, 38, 135, 0.5);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Poppins', sans-serif;
            background:
                linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(51, 65, 85, 0.95) 100%),
                url('/static/images/cargo-ship.jpg') center/cover no-repeat fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* Overlay di sicurezza */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 30% 70%, rgba(255, 107, 107, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(238, 90, 36, 0.15) 0%, transparent 50%);
            z-index: 1;
        }

        /* Container principale */
        .auth-code-container {
            position: relative;
            z-index: 10;
            max-width: 500px;
            width: 95%;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.3),
                0 8px 25px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: var(--transition);
            padding: 45px 40px;
        }

        .auth-code-container:hover {
            transform: translateY(-3px);
            box-shadow:
                0 25px 70px rgba(0, 0, 0, 0.25),
                0 10px 30px rgba(0, 0, 0, 0.1);
        }

        @keyframes slideUp {
            0% {
                opacity: 0;
                transform: translateY(60px) scale(0.95);
                filter: blur(10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        /* Header di sicurezza */
        .security-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .security-icon {
            width: 120px;
            height: 120px;
            background: var(--security-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.3);
            animation: securityPulse 2s ease-in-out infinite;
        }

        .security-icon i {
            font-size: 3.5rem;
            color: white;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        @keyframes securityPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 15px 35px rgba(255, 107, 107, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 20px 45px rgba(255, 107, 107, 0.4);
            }
        }

        .security-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .security-subtitle {
            font-size: 1.1rem;
            color: #4a5568;
            font-weight: 500;
            margin-bottom: 0;
            opacity: 0.9;
        }
        /* Alert messaggi */
        .custom-alert {
            border-radius: var(--border-radius);
            padding: 16px 20px;
            margin-bottom: 24px;
            font-size: 0.95rem;
            font-weight: 500;
            animation: alertSlide 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .custom-alert.error {
            background: rgba(248, 113, 113, 0.15);
            border: 1px solid rgba(248, 113, 113, 0.3);
            color: #dc2626;
        }

        .custom-alert.info {
            background: rgba(59, 130, 246, 0.15);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #2563eb;
        }

        @keyframes alertSlide {
            0% {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .alert-icon {
            font-size: 1.3rem;
            flex-shrink: 0;
        }

        /* Form styling per codice */
        .code-form {
            margin-bottom: 30px;
        }

        .code-input-group {
            position: relative;
            margin-bottom: 28px;
        }

        .code-label {
            font-weight: 600;
            color: #334155;
            margin-bottom: 15px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 10px;
            justify-content: center;
        }

        .code-input {
            width: 100%;
            padding: 20px;
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            letter-spacing: 8px;
            border: 3px solid #e2e8f0;
            border-radius: 15px;
            background: #ffffff;
            color: #1e293b;
            transition: var(--transition);
            font-family: 'Courier New', monospace;
        }

        .code-input:focus {
            border-color: #ff6b6b;
            box-shadow:
                0 0 0 4px rgba(255, 107, 107, 0.1),
                0 4px 12px rgba(0,0,0,0.1);
            background: #ffffff;
            outline: none;
            transform: translateY(-2px);
        }

        .code-input::placeholder {
            color: #94a3b8;
            opacity: 0.6;
            letter-spacing: 4px;
        }

        /* Pulsante verifica */
        .btn-verify {
            background: var(--security-gradient);
            border: none;
            border-radius: 12px;
            padding: 18px 32px;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            width: 100%;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 25px rgba(255, 107, 107, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            letter-spacing: 0.3px;
        }

        .btn-verify::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn-verify:hover::before {
            left: 100%;
        }

        .btn-verify:hover {
            transform: translateY(-3px);
            box-shadow:
                0 12px 30px rgba(255, 107, 107, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);
        }

        .btn-verify:active {
            transform: translateY(-2px);
            transition: transform 0.1s ease;
        }

        .btn-verify:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        /* Info box */
        .info-box {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-top: 25px;
            text-align: center;
        }

        .info-box h6 {
            color: #2563eb;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .info-box p {
            color: #4a5568;
            font-size: 0.9rem;
            margin: 0;
            line-height: 1.5;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .auth-code-container {
                max-width: 420px;
                margin: 20px;
                padding: 35px 25px;
            }

            .security-icon {
                width: 100px;
                height: 100px;
            }

            .security-icon i {
                font-size: 3rem;
            }

            .security-title {
                font-size: 1.8rem;
            }

            .code-input {
                font-size: 1.5rem;
                letter-spacing: 6px;
                padding: 16px;
            }

            .btn-verify {
                padding: 16px 28px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .auth-code-container {
                margin: 10px;
                padding: 30px 20px;
            }

            .security-title {
                font-size: 1.6rem;
            }

            .code-input {
                font-size: 1.3rem;
                letter-spacing: 4px;
                padding: 14px;
            }
        }
    </style>
</head>
<body>
    <!-- Container principale -->
    <div class="auth-code-container">
        <!-- Header di sicurezza -->
        <div class="security-header">
            <div class="security-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h1 class="security-title">Codice di Sicurezza</h1>
            <p class="security-subtitle">Inserisci il codice ricevuto via email per accedere al sistema</p>
        </div>

        <!-- Alert messaggi -->
        {% if error %}
        <div class="custom-alert error">
            <i class="fas fa-exclamation-triangle alert-icon"></i>
            <span>{{ error }}</span>
        </div>
        {% endif %}

        {% if message %}
        <div class="custom-alert info">
            <i class="fas fa-info-circle alert-icon"></i>
            <span>{{ message }}</span>
        </div>
        {% endif %}

        <!-- Form inserimento codice -->
        <form method="post" action="/auth-code" id="codeForm" class="code-form">
            <div class="code-input-group">
                <label for="auth_code" class="code-label">
                    <i class="fas fa-key"></i>
                    Codice di 6 cifre
                </label>
                <input type="text"
                       class="code-input"
                       id="auth_code"
                       name="auth_code"
                       maxlength="6"
                       pattern="[0-9]{6}"
                       required
                       autofocus
                       autocomplete="off"
                       placeholder="000000">
            </div>

            <button type="submit" class="btn btn-verify" id="verifyButton">
                <i class="fas fa-unlock me-2"></i>
                <span class="btn-text">Verifica Codice</span>
            </button>
        </form>

        <!-- Info box -->
        <div class="info-box">
            <h6><i class="fas fa-envelope me-2"></i>Codice inviato via email</h6>
            <p>
                Il codice di sicurezza è stato inviato all'indirizzo email dell'amministratore.<br>
                Controlla la tua casella di posta e inserisci il codice di 6 cifre ricevuto.
            </p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('codeForm');
            const codeInput = document.getElementById('auth_code');
            const verifyButton = document.getElementById('verifyButton');
            const container = document.querySelector('.auth-code-container');

            // Animazione di entrata
            setTimeout(() => {
                container.style.opacity = '1';
                container.style.transform = 'translateY(0) scale(1)';
            }, 100);

            // Formattazione automatica del codice
            codeInput.addEventListener('input', function(e) {
                // Rimuovi tutto tranne i numeri
                let value = e.target.value.replace(/\D/g, '');

                // Limita a 6 cifre
                if (value.length > 6) {
                    value = value.substring(0, 6);
                }

                e.target.value = value;

                // Cambia colore del bordo in base alla validità
                if (value.length === 6) {
                    e.target.style.borderColor = '#22c55e';
                    e.target.style.boxShadow = '0 0 0 4px rgba(34, 197, 94, 0.1)';
                } else if (value.length > 0) {
                    e.target.style.borderColor = '#f59e0b';
                    e.target.style.boxShadow = '0 0 0 4px rgba(245, 158, 11, 0.1)';
                } else {
                    e.target.style.borderColor = '#e2e8f0';
                    e.target.style.boxShadow = 'none';
                }
            });

            // Auto-submit quando il codice è completo
            codeInput.addEventListener('input', function(e) {
                if (e.target.value.length === 6) {
                    setTimeout(() => {
                        form.submit();
                    }, 500); // Piccolo delay per l'effetto visivo
                }
            });

            // Gestione submit form
            form.addEventListener('submit', function(e) {
                const code = codeInput.value.trim();

                // Validazione
                if (code.length !== 6 || !/^\d{6}$/.test(code)) {
                    e.preventDefault();
                    showError('Il codice deve essere composto da esattamente 6 cifre');
                    return;
                }

                // Animazione pulsante
                const btnText = verifyButton.querySelector('.btn-text');
                const btnIcon = verifyButton.querySelector('i');

                btnIcon.className = 'fas fa-spinner fa-spin me-2';
                btnText.textContent = 'Verifica in corso...';
                verifyButton.disabled = true;

                // Animazione container
                container.style.filter = 'blur(1px)';
                container.style.opacity = '0.8';
            });

            // Funzione per mostrare errori
            function showError(message) {
                // Rimuovi errori esistenti
                const existingError = document.querySelector('.custom-alert.error');
                if (existingError) {
                    existingError.remove();
                }

                // Crea nuovo errore
                const errorDiv = document.createElement('div');
                errorDiv.className = 'custom-alert error';
                errorDiv.innerHTML = `
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <span>${message}</span>
                `;

                // Inserisci prima del form
                form.parentNode.insertBefore(errorDiv, form);

                // Focus sull'input
                codeInput.focus();
                codeInput.select();
            }

            // Effetto hover discreto per il container
            container.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });

            container.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0px)';
            });

            // Prevenzione doppio submit
            let isSubmitting = false;
            form.addEventListener('submit', function(e) {
                if (isSubmitting) {
                    e.preventDefault();
                    return false;
                }
                isSubmitting = true;
            });
        });
    </script>
</body>
</html>
